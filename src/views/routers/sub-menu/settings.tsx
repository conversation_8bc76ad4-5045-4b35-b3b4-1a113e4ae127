import { AppstoreOutlined, FileProtectOutlined, ReadOutlined, UnorderedListOutlined } from '@ant-design/icons'
import { IRouter } from '~/routers'
// import NewsView from '../../main/settings/news'
import TermsAndPrivacyView from '~/views/main/settings/terms-and-privacy'
import SubscriptionPlanView from '~/views/main/settings/subscription-plan'
import {ApiDocumentView} from '~/views/main/settings/api-document'

const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  createTitle: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  createTitle,
  isMenu,
  icon,
  children
})

const createMenuItem = (
  path: string,
  element: JSX.Element,
  title: string,
  createTitle: string,
  icon = <UnorderedListOutlined />,
  isMenu?: boolean
): IRouter => createRoute(path, element, title, createTitle, icon, isMenu)

const subMenuSettings = [
  //Danh sách gói dịch vụ
  {
    path: 'plan-service-list',
    title: 'Danh sách gói dịch vụ',
    createTitle: 'Tạo gói dịch vụ',
    view: <SubscriptionPlanView />,
    icon: <AppstoreOutlined />
  },
  //Tin tức
  // {
  //   path: 'news',
  //   title: 'Tin tức',
  //   view: <NewsView />,
  //   icon: <ReadOutlined />
  // },
  //Điều khoản  và bảo mật
  {
    path: 'terms-and-privacy',
    title: 'Điều khoản và bảo mật',
    createTitle: 'Tạo mới điều khoản và bảo mật',
    view: <TermsAndPrivacyView />,
    icon: <FileProtectOutlined />
  },
  {
    path: 'setting-documents',
    title: 'API Document',
    createTitle: 'Tạo mới tài liệu API',
    view: <ApiDocumentView />,
    icon: <FileProtectOutlined />
  },
].map((item) => createMenuItem(item.path, item.view, item.title, item.createTitle, item.icon))

export default subMenuSettings
