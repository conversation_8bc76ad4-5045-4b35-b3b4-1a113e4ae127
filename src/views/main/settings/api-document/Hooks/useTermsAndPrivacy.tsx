import { useInfiniteQuery } from "@tanstack/react-query"
import { apiDocumentService } from " ~/services/ApiDocumentService/apiDocumentService"
import { IApiDocContentList } from "~/dto/apiDocumentContent"

export const useApiDocument = (enabled: boolean = false) => {

  const { data, isFetching, refetch } = useInfiniteQuery<IApiDocContentList>({
    queryKey: [apiDocumentService.APIs.LIST],
    queryFn: async () => {
      return await apiDocumentService.getApiDocumentList()
    },
    getNextPageParam: (lastPage, allPages) => {
      if (lastPage.data.length === 0) return undefined
      return allPages.length + 1
    },
    initialPageParam: 1,
    enabled: enabled
  })

  return {
    data,
    isFetching,
    refetch
  }
}
