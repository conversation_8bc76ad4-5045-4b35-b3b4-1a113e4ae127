import { rootApiService } from '../@common'
import { IApiDocContentCreate, IApiDocContentUpdate } from '~/dto/apiDocumentContent'


export class ApiDocumentService {

    APIs = {
        LIST: `${rootApiService.backEnd}/api/admin/api-doc-content/list`,
        DETAIL: `${rootApiService.backEnd}/api/admin/api-doc-content/detail`,
        CREATE: `${rootApiService.backEnd}/api/admin/api-doc-content`,
        UPDATE: `${rootApiService.backEnd}/api/admin/api-doc-content/update`
    }

    async getApiDocumentList() {
        return await rootApiService.get(this.APIs.LIST, { pageSize: 99999, pageIndex: 1 })
    }

    async getApiDocumentDetail(id: string) {
        return await rootApiService.get(this.APIs.DETAIL, { id })
    }

    async createApiDocument(apiDocument: IApiDocContentCreate) {
        return await rootApiService.post(this.APIs.CREATE, apiDocument)
    }

    async updateApiDocument(apiDocument: IApiDocContentUpdate) {
        return await rootApiService.post(this.APIs.UPDATE, apiDocument)
    }
}

export const apiDocumentService = new ApiDocumentService()
